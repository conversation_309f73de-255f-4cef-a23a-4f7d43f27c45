package config

import (
	"fmt"
	"strings"

	"resumatter/internal/errors"
	"resumatter/internal/security"
)

// VaultConfig holds Vault connection configuration
type VaultConfig = security.VaultConfig

// VaultSecrets defines where to find secrets in Vault
type VaultSecrets = security.VaultSecrets

// VaultSecret represents a secret read from Vault's KVv2 engine.
type VaultSecret = security.VaultSecret

// ApplyVaultSecrets loads secrets from Vault and applies them to the config
func ApplyVaultSecrets(config *Config, logger *errors.Logger) error {
	if !config.Vault.Enabled {
		if logger != nil {
			logger.Debug("Vault integration disabled, skipping secret loading")
		}
		return nil // Vault not enabled, skip
	}

	client, err := initializeVaultClient(config.Vault, logger)
	if err != nil {
		return err
	}
	if client == nil {
		return nil // Not an error, just disabled
	}

	return loadAllSecretsFromVault(client, config, logger)
}

// initializeVaultClient initializes the Vault client with proper logging
func initializeVaultClient(vaultConfig VaultConfig, logger *errors.Logger) (*security.VaultClient, error) {
	if logger != nil {
		logger.Info("Loading secrets from Vault",
			"api_keys_path", vaultConfig.Secrets.APIKeys,
			"gemini_key_path", vaultConfig.Secrets.GeminiKey,
			"tls_certs_path", vaultConfig.Secrets.TLSCerts)
	}

	client, err := security.NewVaultClient(vaultConfig, logger)
	if err != nil {
		if logger != nil {
			logger.LogError(err, "Failed to initialize Vault client")
		}
		return nil, fmt.Errorf("failed to initialize vault client: %w", err)
	}

	return client, nil
}

// loadAllSecretsFromVault loads all configured secrets from Vault
func loadAllSecretsFromVault(client *security.VaultClient, config *Config, logger *errors.Logger) error {
	vaultConfig := config.Vault

	// Load each type of secret
	if err := loadAPIKeysFromVault(client, config, vaultConfig, logger); err != nil {
		return err
	}

	if err := loadGeminiKeyFromVault(client, config, vaultConfig, logger); err != nil {
		return err
	}

	if err := loadTLSCertsFromVault(client, config, vaultConfig, logger); err != nil {
		return err
	}

	if logger != nil {
		logger.Info("Successfully completed applying secrets from Vault")
	}

	return nil
}

// loadAPIKeysFromVault loads API keys from Vault
func loadAPIKeysFromVault(client *security.VaultClient, config *Config, vaultConfig VaultConfig, logger *errors.Logger) error {
	if vaultConfig.Secrets.APIKeys == "" {
		return nil
	}

	if logger != nil {
		logger.Debug("Loading API keys from Vault", "path", vaultConfig.Secrets.APIKeys)
	}

	apiKeys, err := client.GetStringSliceSecret(vaultConfig.Secrets.APIKeys, "keys")
	if err != nil {
		if logger != nil {
			logger.LogError(err, "Failed to load API keys from Vault", "path", vaultConfig.Secrets.APIKeys)
		}
		return fmt.Errorf("failed to load API keys from vault: %w", err)
	}

	if len(apiKeys) > 0 {
		config.Server.APIKeys = apiKeys
		if logger != nil {
			logger.Info("API keys loaded from Vault", "count", len(apiKeys))
		}
	} else {
		if logger != nil {
			logger.Warn("No API keys found in Vault", "path", vaultConfig.Secrets.APIKeys)
		}
	}

	return nil
}

// loadGeminiKeyFromVault loads Gemini API key from Vault
func loadGeminiKeyFromVault(client *security.VaultClient, config *Config, vaultConfig VaultConfig, logger *errors.Logger) error {
	if vaultConfig.Secrets.GeminiKey == "" {
		return nil
	}

	if logger != nil {
		logger.Debug("Loading Gemini API key from Vault", "path", vaultConfig.Secrets.GeminiKey)
	}

	geminiKey, err := client.GetStringSecret(vaultConfig.Secrets.GeminiKey, "api_key")
	if err != nil {
		if logger != nil {
			logger.LogError(err, "Failed to load Gemini API key from Vault", "path", vaultConfig.Secrets.GeminiKey)
		}
		return fmt.Errorf("failed to load Gemini API key from vault: %w", err)
	}

	if geminiKey != "" {
		applyGeminiKeyToConfig(config, geminiKey)
		if logger != nil {
			logger.Info("Gemini API key loaded from Vault and applied to all AI configurations")
		}
	} else {
		if logger != nil {
			logger.Warn("Empty Gemini API key found in Vault", "path", vaultConfig.Secrets.GeminiKey)
		}
	}

	return nil
}

// applyGeminiKeyToConfig applies the Gemini API key to all AI configurations
func applyGeminiKeyToConfig(config *Config, geminiKey string) {
	config.AI.APIKey = geminiKey
	if config.AI.Tailor.APIKey == "" {
		config.AI.Tailor.APIKey = geminiKey
	}
	if config.AI.Evaluate.APIKey == "" {
		config.AI.Evaluate.APIKey = geminiKey
	}
	if config.AI.Analyze.APIKey == "" {
		config.AI.Analyze.APIKey = geminiKey
	}
}

// loadTLSCertsFromVault loads TLS certificates from Vault
func loadTLSCertsFromVault(client *security.VaultClient, config *Config, vaultConfig VaultConfig, logger *errors.Logger) error {
	if vaultConfig.Secrets.TLSCerts == "" {
		return nil
	}

	if logger != nil {
		logger.Debug("Loading TLS certificates from Vault", "path", vaultConfig.Secrets.TLSCerts)
	}

	tlsData, err := client.GetSecretV2(vaultConfig.Secrets.TLSCerts)
	if err != nil {
		if logger != nil {
			logger.LogError(err, "Failed to load TLS certificates from Vault", "path", vaultConfig.Secrets.TLSCerts)
		}
		return fmt.Errorf("failed to load TLS certificates from vault: %w", err)
	}

	certCount := loadTLSCertificateContent(config, tlsData, logger)

	if err := validateTLSDeprecatedFields(tlsData, logger); err != nil {
		return err
	}

	if logger != nil {
		logger.Info("TLS certificates loaded from Vault", "certificates_loaded", certCount)
	}

	return nil
}

// loadTLSCertificateContent loads certificate content from Vault data
func loadTLSCertificateContent(config *Config, tlsData *security.VaultSecret, logger *errors.Logger) int {
	certCount := 0

	certCount += loadSingleCertificate(tlsData, "cert", &config.Server.TLS.CertContent, "TLS certificate content", logger)
	certCount += loadSingleCertificate(tlsData, "key", &config.Server.TLS.KeyContent, "TLS private key content", logger)
	certCount += loadSingleCertificate(tlsData, "ca", &config.Server.TLS.CAContent, "TLS CA certificate content", logger)

	return certCount
}

// loadSingleCertificate loads a single certificate field from Vault data
func loadSingleCertificate(tlsData *security.VaultSecret, key string, target *string, description string, logger *errors.Logger) int {
	if content, ok := tlsData.Data[key].(string); ok && content != "" {
		*target = content
		if logger != nil {
			logger.Debug(description+" loaded from Vault", "content_length", len(content))
		}
		return 1
	}
	return 0
}

// validateTLSDeprecatedFields checks for deprecated TLS field usage
func validateTLSDeprecatedFields(tlsData *security.VaultSecret, logger *errors.Logger) error {
	deprecatedFields := map[string]string{
		"cert_file": "cert_file field is no longer supported in Vault. Use 'cert' field with certificate content instead.",
		"key_file":  "key_file field is no longer supported in Vault. Use 'key' field with private key content instead.",
		"ca_file":   "ca_file field is no longer supported in Vault. Use 'ca' field with CA certificate content instead.",
	}

	for field, message := range deprecatedFields {
		if _, hasField := tlsData.Data[field]; hasField {
			if logger != nil {
				logger.LogError(fmt.Errorf("deprecated field detected"), message)
			}
			return fmt.Errorf("vault TLS configuration error: '%s' field is no longer supported. Store certificate content in '%s' field instead",
				field, strings.TrimSuffix(field, "_file"))
		}
	}

	return nil
}
