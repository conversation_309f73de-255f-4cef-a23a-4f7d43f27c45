package config

import (
	"log/slog"
	"testing"

	"github.com/stretchr/testify/assert"

	"resumatter/internal/errors"
	"resumatter/internal/security"
)

// newMockLogger creates a mock logger for testing
func newMockLogger() *errors.Logger {
	return errors.NewLogger(slog.LevelDebug)
}

// Test applyGeminiKeyToConfig function
func TestApplyGeminiKeyToConfig(t *testing.T) {
	config := &Config{
		AI: AIConfig{
			Tailor:   OperationAIConfig{},
			Evaluate: OperationAIConfig{},
			Analyze:  OperationAIConfig{},
		},
	}

	geminiKey := "test-gemini-key"
	applyGeminiKeyToConfig(config, geminiKey)

	assert.Equal(t, geminiKey, config.AI.APIKey)
	assert.Equal(t, geminiKey, config.AI.Tailor.APIKey)
	assert.Equal(t, geminiKey, config.AI.Evaluate.APIKey)
	assert.Equal(t, gemini<PERSON><PERSON>, config.AI.Analyze.APIKey)
}

func TestApplyGeminiKeyToConfigWithExistingKeys(t *testing.T) {
	existingTailorKey := "existing-tailor-key"
	config := &Config{
		AI: AIConfig{
			Tailor:   OperationAIConfig{APIKey: existingTailorKey},
			Evaluate: OperationAIConfig{},
			Analyze:  OperationAIConfig{},
		},
	}

	geminiKey := "test-gemini-key"
	applyGeminiKeyToConfig(config, geminiKey)

	assert.Equal(t, geminiKey, config.AI.APIKey)
	assert.Equal(t, existingTailorKey, config.AI.Tailor.APIKey) // Should not overwrite existing
	assert.Equal(t, geminiKey, config.AI.Evaluate.APIKey)
	assert.Equal(t, geminiKey, config.AI.Analyze.APIKey)
}

// Test loadSingleCertificate function
func TestLoadSingleCertificate(t *testing.T) {
	logger := newMockLogger()

	tests := []struct {
		name        string
		tlsData     *security.VaultSecret
		key         string
		description string
		expected    int
		expectValue string
	}{
		{
			name: "valid certificate content",
			tlsData: &security.VaultSecret{
				Data: map[string]any{
					"cert": "-----BEGIN CERTIFICATE-----\ntest-cert\n-----END CERTIFICATE-----",
				},
			},
			key:         "cert",
			description: "TLS certificate content",
			expected:    1,
			expectValue: "-----BEGIN CERTIFICATE-----\ntest-cert\n-----END CERTIFICATE-----",
		},
		{
			name: "empty certificate content",
			tlsData: &security.VaultSecret{
				Data: map[string]any{
					"cert": "",
				},
			},
			key:         "cert",
			description: "TLS certificate content",
			expected:    0,
			expectValue: "",
		},
		{
			name: "missing certificate key",
			tlsData: &security.VaultSecret{
				Data: map[string]any{
					"other": "value",
				},
			},
			key:         "cert",
			description: "TLS certificate content",
			expected:    0,
			expectValue: "",
		},
		{
			name: "non-string certificate value",
			tlsData: &security.VaultSecret{
				Data: map[string]any{
					"cert": 123,
				},
			},
			key:         "cert",
			description: "TLS certificate content",
			expected:    0,
			expectValue: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var target string
			result := loadSingleCertificate(tt.tlsData, tt.key, &target, tt.description, logger)

			assert.Equal(t, tt.expected, result)
			assert.Equal(t, tt.expectValue, target)
		})
	}
}

// Test validateTLSDeprecatedFields function
func TestValidateTLSDeprecatedFields(t *testing.T) {
	logger := newMockLogger()

	tests := []struct {
		name        string
		tlsData     *security.VaultSecret
		expectError bool
		errorMsg    string
	}{
		{
			name: "no deprecated fields",
			tlsData: &security.VaultSecret{
				Data: map[string]any{
					"cert": "valid-cert",
					"key":  "valid-key",
				},
			},
			expectError: false,
		},
		{
			name: "cert_file deprecated field",
			tlsData: &security.VaultSecret{
				Data: map[string]any{
					"cert_file": "deprecated-field",
				},
			},
			expectError: true,
			errorMsg:    "cert_file' field is no longer supported",
		},
		{
			name: "key_file deprecated field",
			tlsData: &security.VaultSecret{
				Data: map[string]any{
					"key_file": "deprecated-field",
				},
			},
			expectError: true,
			errorMsg:    "key_file' field is no longer supported",
		},
		{
			name: "ca_file deprecated field",
			tlsData: &security.VaultSecret{
				Data: map[string]any{
					"ca_file": "deprecated-field",
				},
			},
			expectError: true,
			errorMsg:    "ca_file' field is no longer supported",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateTLSDeprecatedFields(tt.tlsData, logger)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test loadTLSCertificateContent function
func TestLoadTLSCertificateContent(t *testing.T) {
	logger := newMockLogger()
	config := &Config{
		Server: ServerConfig{
			TLS: TLSConfig{},
		},
	}

	tlsData := &security.VaultSecret{
		Data: map[string]any{
			"cert": "-----BEGIN CERTIFICATE-----\ntest-cert\n-----END CERTIFICATE-----",
			"key":  "-----BEGIN PRIVATE KEY-----\ntest-key\n-----END PRIVATE KEY-----",
			"ca":   "-----BEGIN CERTIFICATE-----\ntest-ca\n-----END CERTIFICATE-----",
		},
	}

	certCount := loadTLSCertificateContent(config, tlsData, logger)

	assert.Equal(t, 3, certCount)
	assert.Equal(t, "-----BEGIN CERTIFICATE-----\ntest-cert\n-----END CERTIFICATE-----", config.Server.TLS.CertContent)
	assert.Equal(t, "-----BEGIN PRIVATE KEY-----\ntest-key\n-----END PRIVATE KEY-----", config.Server.TLS.KeyContent)
	assert.Equal(t, "-----BEGIN CERTIFICATE-----\ntest-ca\n-----END CERTIFICATE-----", config.Server.TLS.CAContent)
}

func TestLoadTLSCertificateContentPartial(t *testing.T) {
	logger := newMockLogger()
	config := &Config{
		Server: ServerConfig{
			TLS: TLSConfig{},
		},
	}

	tlsData := &security.VaultSecret{
		Data: map[string]any{
			"cert": "-----BEGIN CERTIFICATE-----\ntest-cert\n-----END CERTIFICATE-----",
			// Missing key and ca
		},
	}

	certCount := loadTLSCertificateContent(config, tlsData, logger)

	assert.Equal(t, 1, certCount)
	assert.Equal(t, "-----BEGIN CERTIFICATE-----\ntest-cert\n-----END CERTIFICATE-----", config.Server.TLS.CertContent)
	assert.Equal(t, "", config.Server.TLS.KeyContent)
	assert.Equal(t, "", config.Server.TLS.CAContent)
}

// Test ApplyVaultSecrets function
func TestApplyVaultSecretsDisabled(t *testing.T) {
	logger := newMockLogger()
	config := &Config{
		Vault: VaultConfig{
			Enabled: false,
		},
	}

	err := ApplyVaultSecrets(config, logger)
	assert.NoError(t, err)
}
